'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { signIn } from 'next-auth/react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { Fa<PERSON>ye, FaEyeSlash, FaGithub, FaGoogle } from 'react-icons/fa';
import { FaLinkedinIn, FaXTwitter } from 'react-icons/fa6';
import { toast } from 'react-toastify';
import { z } from 'zod';

// Updated schema for email and password authentication
const signInSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

type SignInFormData = z.infer<typeof signInSchema>;

export default function SignInPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<SignInFormData>({
    resolver: zodResolver(signInSchema),
  });

  const onSubmit: SubmitHandler<SignInFormData> = async data => {
    setIsLoading(true);
    try {
      const result = await signIn('credentials', {
        email: data.email,
        password: data.password,
        redirect: false,
      });

      if (result?.error) {
        toast.error(result.error || 'Invalid credentials');
      } else {
        toast.success('Signed in successfully!');
        router.push('/');
      }
    } catch (error) {
      console.error('Sign in error:', error);
      toast.error('An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSocialSignIn = async (provider: 'github' | 'google') => {
    setIsLoading(true);
    try {
      await signIn(provider, { callbackUrl: '/dashboard' });
    } catch (error) {
      toast.error(`An error occurred with ${provider} sign in.`);
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-[#171717] flex font-sans">
      {' '}
      {/* Main background color */}
      {/* Left Branding Panel */}
      <div className="hidden lg:flex flex-col w-1/2 bg-dot-pattern items-center justify-center p-12 relative">
        {/* Logo */}
        <div className="flex items-center">
          <span className="text-7xl font-bold text-white tracking-tight">typing</span>
          <div className="ml-1 -mt-1 flex items-center justify-center w-10 h-10 bg-red-500 rounded-full">
            <span className="text-white text-sm font-semibold">.co</span>
          </div>
        </div>

        {/* Social Icons at the bottom */}
        <div className="absolute bottom-10 left-10 flex space-x-4">
          <Link href="#" aria-label="X (Twitter)" className="text-gray-400 hover:text-white p-1">
            <FaXTwitter size={20} />
          </Link>
          <span className="text-gray-500 text-xl leading-none select-none" aria-hidden="true">
            •
          </span>
          <Link href="#" aria-label="LinkedIn" className="text-gray-400 hover:text-white p-1">
            <FaLinkedinIn size={20} />
          </Link>
        </div>
      </div>
      {/* Right Form Panel */}
      <div className="w-full lg:w-1/2 bg-[#212121] flex flex-col items-center justify-center p-6 sm:p-10 relative">
        <div className="w-full max-w-sm">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-white mb-2">Welcome Back</h1>
            <p className="text-gray-400">Sign in to your account</p>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-5">
            <div>
              <input
                {...register('email')}
                type="email"
                className="w-full px-4 py-3 bg-[#2C2C2C] border border-transparent text-gray-300 placeholder-gray-500 focus:ring-2 focus:ring-orange-500 focus:border-transparent rounded-xl outline-none transition-all"
                placeholder="Email address"
              />
              {errors.email && (
                <p className="mt-1.5 text-xs text-red-400">{errors.email.message}</p>
              )}
            </div>

            <div className="relative">
              <input
                {...register('password')}
                type={showPassword ? 'text' : 'password'}
                className="w-full px-4 py-3 pr-12 bg-[#2C2C2C] border border-transparent text-gray-300 placeholder-gray-500 focus:ring-2 focus:ring-orange-500 focus:border-transparent rounded-xl outline-none transition-all"
                placeholder="Password"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
              >
                {showPassword ? <FaEyeSlash size={18} /> : <FaEye size={18} />}
              </button>
              {errors.password && (
                <p className="mt-1.5 text-xs text-red-400">{errors.password.message}</p>
              )}
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-gradient-to-r from-red-500 to-orange-500 hover:opacity-90 disabled:opacity-60 text-white font-semibold py-3 px-4 rounded-full flex items-center justify-center transition-opacity shadow-lg"
            >
              {isLoading ? (
                <svg
                  className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
              ) : (
                'Sign In'
              )}
            </button>
          </form>

          {/* Helper Links */}
          <div className="mt-5 flex justify-between items-center text-xs">
            <span className="text-gray-400">
              Don't have an account yet?{' '}
              <Link href="/sign-up" className="font-semibold text-orange-400 hover:text-orange-300">
                Register now!
              </Link>
            </span>
            <Link href="/forgot-password" className="text-gray-500 hover:text-gray-300">
              Forgot Password?
            </Link>
          </div>

          {/* Divider */}
          <div className="my-8 flex items-center">
            <hr className="flex-grow border-t border-gray-600" />
            <span className="mx-3 text-gray-500 text-xs">OR</span>
            <hr className="flex-grow border-t border-gray-600" />
          </div>

          {/* Social Login Buttons */}
          <div className="space-y-3">
            <button
              onClick={() => handleSocialSignIn('github')}
              disabled={isLoading}
              className="w-full bg-[#2C2C2C] hover:bg-[#3A3A3A] disabled:opacity-60 text-gray-300 font-medium py-2.5 px-4 rounded-lg flex items-center justify-center transition-colors"
            >
              <FaGithub className="mr-3 text-white" size={18} />
              Sign in with GitHub
            </button>
            <button
              onClick={() => handleSocialSignIn('google')}
              disabled={isLoading}
              className="w-full bg-[#2C2C2C] hover:bg-[#3A3A3A] disabled:opacity-60 text-gray-300 font-medium py-2.5 px-4 rounded-lg flex items-center justify-center transition-colors"
            >
              <FaGoogle className="mr-3 text-[#DB4437]" size={18} />
              Sign in with Google
            </button>
          </div>
        </div>

        {/* Footer Links within Form Panel */}
        <div className="absolute bottom-6 sm:bottom-8 text-center w-full max-w-sm px-4">
          <div className="flex flex-col sm:flex-row justify-center items-center text-[11px] text-gray-500 space-y-1 sm:space-y-0 sm:space-x-3">
            <Link href="/terms" className="hover:text-gray-300">
              Terms & Conditions
            </Link>
            <span className="hidden sm:inline">|</span>
            <Link href="/privacy" className="hover:text-gray-300">
              Privacy Policy
            </Link>
          </div>
          <p className="mt-2 text-[11px] text-gray-600">
            © {new Date().getFullYear()} Typing.com.co. All rights reserved.
          </p>
        </div>
      </div>
      {/* CSS for dot pattern - add this to your global CSS or a style tag */}
      <style jsx global>{`
        .bg-dot-pattern {
          background-image: radial-gradient(circle, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
          background-size: 15px 15px; /* Adjust size of dots and spacing */
        }
      `}</style>
    </div>
  );
}
